import Link from "next/link";

export default function HeroSection() {
  return (
    <main className="text-center mt-8 sm:mt-8">
      <div className="max-w-3xl mx-auto">
        <h2 className="text-4xl sm:text-6xl font-bold tracking-tight bg-gradient-to-r from-gray-800 to-gray-600 text-transparent bg-clip-text">
          Go from being busy to being effective.
        </h2>
        <p className="mt-6 text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
          Clarity is a life results system designed to help you achieve balance and focus. Move beyond simple task lists and start defining the
          outcomes that truly matter in your life.
        </p>
        <div className="mt-10">
          <Link
            href="/signup"
            className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block"
          >
            Get Started for Free
          </Link>
        </div>
      </div>
    </main>
  );
}
