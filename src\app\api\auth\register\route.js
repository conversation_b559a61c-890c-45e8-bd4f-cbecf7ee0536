import { NextResponse } from "next/server";
// Import the named prisma instance
import prisma from "@/lib/prisma"; // Assuming '@/' is configured to point to 'src/'
import bcrypt from "bcryptjs";

export async function POST(request) {
  console.log(request);
  try {
    const body = await request.json();
    const { username, email, password, first_name, last_name } = body;

    // 1. Validate input
    if (!username || !email || !password) {
      return NextResponse.json({ message: "Missing required fields" }, { status: 400 });
    }

    // 2. Check if user already exists
    const existingUserByEmail = await prisma.auth_user.findUnique({ where: { email } });
    if (existingUserByEmail) {
      return NextResponse.json({ message: "User with this email already exists" }, { status: 409 });
    }

    const existingUserByUsername = await prisma.auth_user.findUnique({ where: { username } });
    if (existingUserByUsername) {
      return NextResponse.json({ message: "Username is already taken" }, { status: 409 });
    }

    // 3. Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // 4. Create the new user in the database
    const newUser = await prisma.auth_user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        first_name: first_name || "",
        last_name: last_name || "",
      },
    });

    // 5. Return the created user (without the password)
    const { password: _, ...userWithoutPassword } = newUser;
    return NextResponse.json(userWithoutPassword, { status: 201 });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
