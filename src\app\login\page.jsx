"use client";
import Link from "next/link";
import { LogIn, CheckCircle } from "lucide-react";

export default function LoginPage() {
  const handleSubmit = (event) => {
    event.preventDefault();
    // TODO: Implement login logic here.
    // This is where you would call a function from your authentication
    // library (e.g., signIn('credentials', { email, password }) from NextAuth.js).
    console.log("Login form submitted");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
      <div className="w-full max-w-md p-8 bg-white rounded-xl shadow-lg">
        <Link href="/" className="flex justify-center items-center mb-6 group">
          <CheckCircle className="w-8 h-8 text-blue-600 group-hover:text-blue-700 transition-colors" />
          <h1 className="text-3xl font-bold text-gray-800 ml-2 group-hover:text-gray-900 transition-colors">Clarity</h1>
        </Link>
        <h2 className="text-2xl font-semibold text-center text-gray-700 mb-6">Welcome Back</h2>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-600 mb-1">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-black transition"
            />
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-medium text-gray-600 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-black transition"
            />
            <a href="#" className="text-xs text-blue-600 hover:underline mt-1 block text-right">
              Forgot Password?
            </a>
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2.5 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 flex items-center justify-center"
          >
            <LogIn className="w-5 h-5 mr-2" />
            Log In
          </button>
        </form>
      </div>
      <p className="mt-6 text-center text-sm text-gray-600">
        Don't have an account?{" "}
        <Link href="/signup" className="font-semibold text-blue-600 hover:underline">
          Sign Up
        </Link>
      </p>
    </div>
  );
}
